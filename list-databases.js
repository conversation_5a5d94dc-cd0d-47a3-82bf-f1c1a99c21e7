// Script to list all databases in the cluster
const { MongoClient } = require('mongodb');
require('dotenv').config({ path: '.env.local' });

async function listDatabases() {
  const mongoUri = process.env.DATABASE || process.env.MONGODB_URI;
  const client = new MongoClient(mongoUri);
  
  try {
    await client.connect();
    
    console.log('🗄️  All Databases in Cluster');
    console.log('============================');
    
    // List all databases
    const adminDb = client.db().admin();
    const databases = await adminDb.listDatabases();
    
    console.log(`📊 Total databases: ${databases.databases.length}`);
    console.log(`💾 Total size: ${(databases.totalSize / 1024 / 1024).toFixed(2)} MB`);
    console.log('');
    
    databases.databases.forEach((db, index) => {
      const sizeInMB = (db.sizeOnDisk / 1024 / 1024).toFixed(2);
      console.log(`${index + 1}. 📁 ${db.name}: ${sizeInMB} MB`);
    });
    
    console.log('');
    console.log('🎯 Target database for Nevis: Crevodb');
    
    // Check if nevis_ai database exists (from your config)
    const nevisDb = databases.databases.find(db => db.name === 'nevis_ai');
    if (nevisDb) {
      console.log(`📋 nevis_ai database: ${(nevisDb.sizeOnDisk / 1024 / 1024).toFixed(2)} MB`);
    } else {
      console.log('⚠️  nevis_ai database not found');
    }
    
  } catch (error) {
    console.error('❌ Error listing databases:', error);
  } finally {
    await client.close();
  }
}

listDatabases();
