// Script to reset password for an existing user
const { MongoClient } = require('mongodb');
const bcrypt = require('bcryptjs');
require('dotenv').config({ path: '.env.local' });

async function resetPassword() {
  const mongoUri = process.env.DATABASE || process.env.MONGODB_URI;
  const client = new MongoClient(mongoUri);
  
  try {
    await client.connect();
    const db = client.db('nevis_ai');
    const usersCollection = db.collection('users');
    
    // Email to reset password for
    const email = '<EMAIL>';  // Change this to the email you want to reset
    const newPassword = 'password123';     // New password
    
    console.log('🔐 Resetting Password');
    console.log('====================');
    console.log(`📧 Email: ${email}`);
    console.log(`🔑 New Password: ${newPassword}`);
    console.log('');
    
    // Check if user exists
    const user = await usersCollection.findOne({ email });
    if (!user) {
      console.log('❌ User not found with email:', email);
      return;
    }
    
    console.log('👤 User found:', user.displayName || 'No name');
    console.log('🆔 User ID:', user.userId);
    
    // Hash new password
    const hashedPassword = await bcrypt.hash(newPassword, 10);
    
    // Update password
    const result = await usersCollection.updateOne(
      { email },
      { 
        $set: { 
          hashedPassword,
          updatedAt: new Date()
        } 
      }
    );
    
    if (result.modifiedCount > 0) {
      console.log('✅ Password updated successfully!');
      console.log('');
      console.log('🚀 You can now log in with:');
      console.log(`📧 Email: ${email}`);
      console.log(`🔑 Password: ${newPassword}`);
    } else {
      console.log('❌ Failed to update password');
    }
    
  } catch (error) {
    console.error('❌ Error resetting password:', error);
  } finally {
    await client.close();
  }
}

resetPassword();
