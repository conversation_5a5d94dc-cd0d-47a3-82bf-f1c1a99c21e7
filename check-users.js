// Script to check users in the database
const { MongoClient } = require('mongodb');
require('dotenv').config({ path: '.env.local' });

async function checkUsers() {
  const mongoUri = process.env.DATABASE || process.env.MONGODB_URI;
  const client = new MongoClient(mongoUri);
  
  try {
    await client.connect();
    const db = client.db('nevis_ai');
    const usersCollection = db.collection('users');
    
    console.log('👥 Users in Database');
    console.log('===================');
    
    // Count users
    const userCount = await usersCollection.countDocuments();
    console.log(`📊 Total users: ${userCount}`);
    
    if (userCount > 0) {
      // Get first 5 users (without passwords)
      const users = await usersCollection.find({}, {
        projection: { 
          hashedPassword: 0  // Exclude password field
        },
        limit: 5
      }).toArray();
      
      console.log('\n📋 Sample users:');
      users.forEach((user, index) => {
        console.log(`${index + 1}. 📧 ${user.email} (ID: ${user.userId})`);
        console.log(`   👤 Name: ${user.displayName || 'No name'}`);
        console.log(`   📅 Created: ${user.createdAt || 'Unknown'}`);
        console.log('');
      });
      
      console.log('✅ You should be able to log in with any of these email addresses');
      console.log('🔑 If you forgot the password, you may need to reset it or create a new user');
    } else {
      console.log('⚠️  No users found in database');
      console.log('💡 You need to create a user account first');
    }
    
  } catch (error) {
    console.error('❌ Error checking users:', error);
  } finally {
    await client.close();
  }
}

checkUsers();
