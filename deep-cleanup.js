// Deep analysis and cleanup of MongoDB cluster
const { MongoClient } = require('mongodb');
require('dotenv').config({ path: '.env.local' });

async function deepAnalysis() {
  const mongoUri = process.env.DATABASE || process.env.MONGODB_URI;
  const client = new MongoClient(mongoUri);
  
  try {
    await client.connect();
    
    console.log('🔍 Deep Database Analysis');
    console.log('=========================');
    
    // List all databases
    const adminDb = client.db().admin();
    const databases = await adminDb.listDatabases();
    
    console.log('📊 All Databases in Cluster:');
    databases.databases.forEach(db => {
      const sizeInMB = (db.sizeOnDisk / 1024 / 1024).toFixed(2);
      console.log(`   📁 ${db.name}: ${sizeInMB} MB`);
    });
    console.log('');
    
    // Focus on nevis_ai database
    const db = client.db('nevis_ai');
    
    // Check for GridFS collections (file storage)
    console.log('🗃️  Checking for GridFS collections:');
    const collections = await db.listCollections().toArray();
    const gridfsCollections = collections.filter(col => 
      col.name.startsWith('fs.') || col.name.includes('gridfs') || col.name.includes('files')
    );
    
    if (gridfsCollections.length > 0) {
      console.log('📁 Found GridFS collections:');
      for (const col of gridfsCollections) {
        const count = await db.collection(col.name).countDocuments();
        console.log(`   📦 ${col.name}: ${count} documents`);
        
        // If it's a files collection, check sizes
        if (col.name.includes('files') || col.name === 'fs.files') {
          const files = await db.collection(col.name).find({}).limit(5).toArray();
          let totalSize = 0;
          files.forEach(file => {
            if (file.length) totalSize += file.length;
          });
          console.log(`   💾 Sample files total size: ${(totalSize / 1024 / 1024).toFixed(2)} MB`);
        }
      }
    } else {
      console.log('   ✅ No GridFS collections found');
    }
    console.log('');
    
    // Check for large documents with embedded data
    console.log('🔍 Checking for large documents:');
    for (const collection of collections) {
      if (!collection.name.startsWith('fs.')) {
        const coll = db.collection(collection.name);
        const docs = await coll.find({}).limit(10).toArray();
        
        docs.forEach((doc, index) => {
          const docSize = JSON.stringify(doc).length;
          if (docSize > 100000) { // > 100KB
            console.log(`   📄 Large document in ${collection.name}[${index}]: ${(docSize / 1024).toFixed(2)} KB`);
            
            // Check for large fields
            Object.keys(doc).forEach(key => {
              if (typeof doc[key] === 'string' && doc[key].length > 50000) {
                console.log(`      🔸 Large field '${key}': ${(doc[key].length / 1024).toFixed(2)} KB`);
              }
            });
          }
        });
      }
    }
    console.log('');
    
    // Check database stats
    try {
      const stats = await db.stats();
      console.log('📊 Database Statistics:');
      console.log(`   💾 Data Size: ${(stats.dataSize / 1024 / 1024).toFixed(2)} MB`);
      console.log(`   🗃️  Storage Size: ${(stats.storageSize / 1024 / 1024).toFixed(2)} MB`);
      console.log(`   📋 Index Size: ${(stats.indexSize / 1024 / 1024).toFixed(2)} MB`);
      console.log(`   📄 Collections: ${stats.collections}`);
      console.log(`   📊 Objects: ${stats.objects}`);
      console.log(`   📈 Avg Object Size: ${(stats.avgObjSize / 1024).toFixed(2)} KB`);
    } catch (error) {
      console.log('⚠️  Could not get database stats:', error.message);
    }
    
  } catch (error) {
    console.error('❌ Error in deep analysis:', error);
  } finally {
    await client.close();
  }
}

async function aggressiveCleanup() {
  const mongoUri = process.env.DATABASE || process.env.MONGODB_URI;
  const client = new MongoClient(mongoUri);
  
  try {
    await client.connect();
    const db = client.db('nevis_ai');
    
    console.log('🧹 Aggressive Cleanup');
    console.log('=====================');
    
    let totalDeleted = 0;
    let spaceFreed = 0;
    
    // 1. Clean up old generated posts
    try {
      const generatedPosts = db.collection('generatedPosts');
      const oldPosts = await generatedPosts.find({}).toArray();
      
      // Keep only the 50 most recent posts
      if (oldPosts.length > 50) {
        const sortedPosts = oldPosts.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
        const postsToDelete = sortedPosts.slice(50);
        
        console.log(`🗑️  Deleting ${postsToDelete.length} old generated posts (keeping 50 most recent)...`);
        
        const idsToDelete = postsToDelete.map(post => post._id);
        const result = await generatedPosts.deleteMany({
          _id: { $in: idsToDelete }
        });
        
        totalDeleted += result.deletedCount;
        console.log(`✅ Deleted ${result.deletedCount} old posts`);
      }
    } catch (error) {
      console.log('⚠️  Error cleaning generated posts:', error.message);
    }
    
    // 2. Clean up inactive brand profiles
    try {
      const brandProfiles = db.collection('brandProfiles');
      const inactiveBrands = await brandProfiles.countDocuments({ isActive: false });
      
      if (inactiveBrands > 0) {
        console.log(`🗑️  Deleting ${inactiveBrands} inactive brand profiles...`);
        const result = await brandProfiles.deleteMany({ isActive: false });
        totalDeleted += result.deletedCount;
        console.log(`✅ Deleted ${result.deletedCount} inactive brands`);
      }
    } catch (error) {
      console.log('⚠️  Error cleaning brand profiles:', error.message);
    }
    
    // 3. Clean up any GridFS files
    try {
      const collections = await db.listCollections().toArray();
      const gridfsFiles = collections.find(col => col.name === 'fs.files');
      const gridfsChunks = collections.find(col => col.name === 'fs.chunks');
      
      if (gridfsFiles && gridfsChunks) {
        const filesCount = await db.collection('fs.files').countDocuments();
        const chunksCount = await db.collection('fs.chunks').countDocuments();
        
        console.log(`🗑️  Found GridFS files: ${filesCount} files, ${chunksCount} chunks`);
        console.log('🗑️  Deleting all GridFS files...');
        
        await db.collection('fs.files').deleteMany({});
        await db.collection('fs.chunks').deleteMany({});
        
        console.log(`✅ Deleted all GridFS files and chunks`);
        spaceFreed += 100; // Estimate
      }
    } catch (error) {
      console.log('⚠️  Error cleaning GridFS:', error.message);
    }
    
    console.log('');
    console.log(`🎉 Cleanup completed!`);
    console.log(`📊 Documents deleted: ${totalDeleted}`);
    console.log(`💾 Estimated space freed: ~${spaceFreed} MB`);
    console.log('');
    console.log('💡 Try logging in again now!');
    
  } catch (error) {
    console.error('❌ Error during aggressive cleanup:', error);
  } finally {
    await client.close();
  }
}

// Check command line arguments
const args = process.argv.slice(2);
const action = args.find(arg => arg.startsWith('--action='))?.split('=')[1];

if (action === 'cleanup') {
  aggressiveCleanup();
} else {
  deepAnalysis();
}
