// Test the full brand analysis functionality
import fetch from 'node-fetch';

async function testBrandAnalysis() {
  console.log('🧪 Testing Brand Analysis');
  console.log('=========================');

  const testWebsites = [
    'https://example.com',
    'https://github.com',
    'https://stackoverflow.com'
  ];

  for (const website of testWebsites) {
    console.log(`\n🏢 Testing brand analysis for: ${website}`);
    console.log('-'.repeat(60));

    try {
      const response = await fetch('http://localhost:3001/api/analyze-brand', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          websiteUrl: website,
          designImageUris: []
        })
      });

      const result = await response.json();

      if (result.success) {
        console.log('✅ Brand analysis successful!');
        console.log(`📊 Business Name: ${result.data.businessName || 'Not detected'}`);
        console.log(`📝 Description: ${result.data.description?.substring(0, 100) || 'Not detected'}...`);
        console.log(`🎯 Target Audience: ${result.data.targetAudience?.substring(0, 100) || 'Not detected'}...`);
        console.log(`🛠️  Services: ${result.data.services?.slice(0, 3).join(', ') || 'Not detected'}`);
        console.log(`📞 Contact: ${result.data.contactInfo?.email || 'Not detected'}`);
      } else {
        console.log(`❌ Analysis failed: ${result.error}`);
        console.log(`🔍 Error type: ${result.errorType}`);
      }

    } catch (error) {
      console.log(`💥 Request error: ${error.message}`);
    }

    // Wait between requests
    await new Promise(resolve => setTimeout(resolve, 2000));
  }

  console.log('\n🎯 Brand analysis test completed!');
}

// Run the test
testBrandAnalysis().catch(console.error);
