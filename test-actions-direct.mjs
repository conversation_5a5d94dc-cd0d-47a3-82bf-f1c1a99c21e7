// Test the analyzeBrandAction directly
import { analyzeBrandAction } from './src/app/actions.js';

async function testAnalyzeBrandAction() {
  console.log('🧪 Testing analyzeBrandAction');
  console.log('==============================');

  const testWebsites = [
    'https://example.com',
    'https://github.com'
  ];

  for (const website of testWebsites) {
    console.log(`\n🏢 Testing: ${website}`);
    console.log('-'.repeat(50));

    try {
      const result = await analyzeBrandAction(website, []);

      if (result.success) {
        console.log('✅ Analysis successful!');
        console.log(`📊 Business Name: ${result.data.businessName || 'Not detected'}`);
        console.log(`📝 Description: ${result.data.description?.substring(0, 100) || 'Not detected'}...`);
        console.log(`🎯 Target Audience: ${result.data.targetAudience?.substring(0, 100) || 'Not detected'}...`);
        console.log(`🛠️  Services: ${result.data.services?.slice(0, 3).join(', ') || 'Not detected'}`);
      } else {
        console.log(`❌ Analysis failed: ${result.error}`);
        console.log(`🔍 Error type: ${result.errorType}`);
      }

    } catch (error) {
      console.log(`💥 Error: ${error.message}`);
    }

    // Wait between requests
    await new Promise(resolve => setTimeout(resolve, 2000));
  }

  console.log('\n🎯 Test completed!');
}

// Run the test
testAnalyzeBrandAction().catch(console.error);
