// Script to analyze and clean up MongoDB database
const { MongoClient } = require('mongodb');
require('dotenv').config({ path: '.env.local' });

async function analyzeAndCleanup() {
  const mongoUri = process.env.DATABASE || process.env.MONGODB_URI;
  const client = new MongoClient(mongoUri);
  
  try {
    await client.connect();
    const db = client.db('nevis_ai');
    
    console.log('🧹 Database Cleanup Analysis');
    console.log('============================');
    
    // Get all collections
    const collections = await db.listCollections().toArray();
    console.log(`📚 Found ${collections.length} collections`);
    console.log('');
    
    const collectionSizes = [];
    
    // Analyze each collection
    for (const collection of collections) {
      const collName = collection.name;
      const coll = db.collection(collName);
      
      try {
        const count = await coll.countDocuments();
        
        // Get sample documents to understand structure
        const samples = await coll.find({}).limit(3).toArray();
        
        // Estimate size by sampling
        let estimatedSize = 0;
        if (samples.length > 0) {
          const avgDocSize = JSON.stringify(samples).length / samples.length;
          estimatedSize = avgDocSize * count;
        }
        
        collectionSizes.push({
          name: collName,
          count,
          estimatedSizeMB: (estimatedSize / 1024 / 1024).toFixed(2),
          samples: samples.map(doc => {
            // Remove large fields for display
            const cleaned = { ...doc };
            Object.keys(cleaned).forEach(key => {
              if (typeof cleaned[key] === 'string' && cleaned[key].length > 100) {
                cleaned[key] = cleaned[key].substring(0, 100) + '... (truncated)';
              }
            });
            return cleaned;
          })
        });
        
        console.log(`📦 ${collName}:`);
        console.log(`   📊 Documents: ${count}`);
        console.log(`   💾 Estimated size: ${(estimatedSize / 1024 / 1024).toFixed(2)} MB`);
        
        if (samples.length > 0) {
          console.log(`   📄 Sample document keys: ${Object.keys(samples[0]).join(', ')}`);
        }
        console.log('');
        
      } catch (error) {
        console.log(`❌ Error analyzing ${collName}:`, error.message);
      }
    }
    
    // Sort by estimated size
    collectionSizes.sort((a, b) => parseFloat(b.estimatedSizeMB) - parseFloat(a.estimatedSizeMB));
    
    console.log('🏆 Collections by Size (largest first):');
    console.log('======================================');
    collectionSizes.forEach((col, index) => {
      console.log(`${index + 1}. ${col.name}: ${col.estimatedSizeMB} MB (${col.count} docs)`);
    });
    
    console.log('');
    console.log('🔍 Cleanup Recommendations:');
    console.log('===========================');
    
    // Provide specific cleanup recommendations
    for (const col of collectionSizes) {
      if (parseFloat(col.estimatedSizeMB) > 50) {
        console.log(`🎯 ${col.name} (${col.estimatedSizeMB} MB):`);
        
        if (col.name === 'generatedPosts' || col.name === 'generated_posts') {
          console.log('   💡 Consider deleting old generated posts older than 30 days');
          console.log('   💡 Remove posts with large image data');
        } else if (col.name === 'artifacts') {
          console.log('   💡 Consider deleting unused artifacts');
          console.log('   💡 Remove artifacts with large file data');
        } else if (col.name.includes('log') || col.name.includes('analytics')) {
          console.log('   💡 Consider deleting old log/analytics data');
        } else {
          console.log('   💡 Review and delete unnecessary documents');
        }
        console.log('');
      }
    }
    
    // Show specific cleanup commands
    console.log('🛠️  Cleanup Commands:');
    console.log('====================');
    console.log('Run this script with cleanup actions:');
    console.log('node cleanup-database.js --action=cleanup');
    
  } catch (error) {
    console.error('❌ Error analyzing database:', error);
  } finally {
    await client.close();
  }
}

async function performCleanup() {
  const mongoUri = process.env.DATABASE || process.env.MONGODB_URI;
  const client = new MongoClient(mongoUri);
  
  try {
    await client.connect();
    const db = client.db('nevis_ai');
    
    console.log('🧹 Performing Database Cleanup');
    console.log('==============================');
    
    let totalDeleted = 0;
    
    // Clean up old generated posts (older than 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    try {
      const generatedPosts = db.collection('generatedPosts');
      const oldPostsCount = await generatedPosts.countDocuments({
        createdAt: { $lt: thirtyDaysAgo }
      });
      
      if (oldPostsCount > 0) {
        console.log(`🗑️  Deleting ${oldPostsCount} old generated posts...`);
        const result = await generatedPosts.deleteMany({
          createdAt: { $lt: thirtyDaysAgo }
        });
        totalDeleted += result.deletedCount;
        console.log(`✅ Deleted ${result.deletedCount} old posts`);
      }
    } catch (error) {
      console.log('⚠️  No generatedPosts collection or error:', error.message);
    }
    
    // Clean up old artifacts
    try {
      const artifacts = db.collection('artifacts');
      const oldArtifactsCount = await artifacts.countDocuments({
        createdAt: { $lt: thirtyDaysAgo }
      });
      
      if (oldArtifactsCount > 0) {
        console.log(`🗑️  Deleting ${oldArtifactsCount} old artifacts...`);
        const result = await artifacts.deleteMany({
          createdAt: { $lt: thirtyDaysAgo }
        });
        totalDeleted += result.deletedCount;
        console.log(`✅ Deleted ${result.deletedCount} old artifacts`);
      }
    } catch (error) {
      console.log('⚠️  No artifacts collection or error:', error.message);
    }
    
    console.log('');
    console.log(`🎉 Cleanup completed! Deleted ${totalDeleted} documents total`);
    console.log('💡 You should now have more space available');
    
  } catch (error) {
    console.error('❌ Error during cleanup:', error);
  } finally {
    await client.close();
  }
}

// Check command line arguments
const args = process.argv.slice(2);
const action = args.find(arg => arg.startsWith('--action='))?.split('=')[1];

if (action === 'cleanup') {
  performCleanup();
} else {
  analyzeAndCleanup();
}
