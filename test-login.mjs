// Test login functionality directly
import fetch from 'node-fetch';

async function testLogin() {
  console.log('🧪 Testing Login API');
  console.log('===================');

  const loginData = {
    email: '<EMAIL>',  // Using one of the existing users
    password: 'password123'  // Updated password
  };

  try {
    console.log('📡 Sending login request...');
    const response = await fetch('http://localhost:3001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(loginData),
    });

    console.log(`📊 Response status: ${response.status}`);

    const data = await response.json();
    console.log('📋 Response data:', JSON.stringify(data, null, 2));

    if (response.ok && data.success) {
      console.log('✅ Login successful!');
      console.log('🎫 Token received:', data.token ? 'Yes' : 'No');
    } else {
      console.log('❌ Login failed');
      console.log('💬 Error:', data.error);
    }

  } catch (error) {
    console.error('❌ Network error:', error.message);

    if (error.code === 'ECONNREFUSED') {
      console.log('🔌 Server is not running on port 3001');
      console.log('💡 Try starting the server with: npm run dev');
    }
  }
}

// Also test if server is responding at all
async function testServerHealth() {
  console.log('\n🏥 Testing Server Health');
  console.log('========================');

  try {
    const response = await fetch('http://localhost:3001/api/debug-users');
    console.log(`📊 Debug endpoint status: ${response.status}`);

    if (response.ok) {
      const data = await response.json();
      console.log('✅ Server is responding');
      console.log(`👥 Users in database: ${data.totalUsers || 'Unknown'}`);
    } else {
      console.log('⚠️ Server responded with error');
      const text = await response.text();
      console.log('📄 Error response:', text.substring(0, 200));
    }
  } catch (error) {
    console.error('❌ Server health check failed:', error.message);
  }
}

async function runTests() {
  await testServerHealth();
  await testLogin();
}

runTests();
