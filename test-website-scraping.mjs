// Test the new website scraping functionality
import fetch from 'node-fetch';

async function testWebsiteScraping() {
  console.log('🧪 Testing Website Scraping API');
  console.log('================================');

  const testUrls = [
    'https://example.com',
    'https://google.com',
    'https://github.com',
    'https://stackoverflow.com',
    'invalid-url'
  ];

  for (const url of testUrls) {
    console.log(`\n🌐 Testing: ${url}`);
    console.log('-'.repeat(50));

    try {
      const response = await fetch('http://localhost:3001/api/scrape-website', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url })
      });

      const result = await response.json();

      if (result.success) {
        console.log(`✅ Success: ${result.content.length} characters scraped`);
        console.log(`📄 Preview: ${result.content.substring(0, 200)}...`);
      } else {
        console.log(`❌ Failed: ${result.error}`);
        if (result.blocked) console.log('🚫 Reason: Website blocks access');
        if (result.timeout) console.log('⏰ Reason: Timeout');
        if (result.unreachable) console.log('🔌 Reason: Unreachable');
      }

    } catch (error) {
      console.log(`💥 Error: ${error.message}`);
    }
  }

  console.log('\n🎯 Test completed!');
}

// Run the test
testWebsiteScraping().catch(console.error);
