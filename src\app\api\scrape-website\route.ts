import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { url } = await request.json();

    if (!url) {
      return NextResponse.json(
        { error: 'URL is required' },
        { status: 400 }
      );
    }

    // Validate URL format
    let validUrl = url.trim();
    if (!validUrl.startsWith('http://') && !validUrl.startsWith('https://')) {
      validUrl = 'https://' + validUrl;
    }

    // Validate URL
    try {
      new URL(validUrl);
    } catch {
      return NextResponse.json(
        { error: 'Invalid URL format' },
        { status: 400 }
      );
    }

    console.log(`🌐 Scraping website: ${validUrl}`);

    // Import cheerio for HTML parsing
    const cheerio = await import('cheerio');

    // Use fetch to get the website content with proper headers
    const response = await fetch(validUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
      },
      // Add timeout
      signal: AbortSignal.timeout(15000), // 15 second timeout
    });

    if (!response.ok) {
      console.log(`❌ HTTP error: ${response.status} ${response.statusText}`);
      return NextResponse.json(
        { 
          error: `Website returned ${response.status}: ${response.statusText}`,
          blocked: response.status === 403 || response.status === 429
        },
        { status: response.status === 403 ? 200 : response.status } // Return 200 for blocked sites so we can handle gracefully
      );
    }

    const html = await response.text();
    const $ = cheerio.load(html);

    // Remove unwanted elements
    $('script, style, nav, footer, header, .cookie-banner, .popup, .modal, .advertisement, .ads').remove();

    // Extract comprehensive content
    const extractedContent = {
      // Basic page info
      title: $('title').text().trim() || $('h1').first().text().trim(),
      metaDescription: $('meta[name="description"]').attr('content') || '',
      
      // Main content areas
      heroSection: $('section:first, .hero, .banner, .jumbotron, .main-banner').first().text().trim(),
      aboutSection: $('section:contains("About"), div:contains("About"), .about, #about').text().trim(),
      servicesSection: $('section:contains("Services"), div:contains("Services"), .services, #services, section:contains("What we do"), div:contains("What we do")').text().trim(),
      contactSection: $('section:contains("Contact"), div:contains("Contact"), .contact, #contact').text().trim(),
      
      // Business-specific content
      teamSection: $('section:contains("Team"), div:contains("Team"), .team, #team, section:contains("Staff"), div:contains("Staff")').text().trim(),
      testimonialsSection: $('section:contains("Testimonial"), div:contains("Testimonial"), .testimonials, #testimonials, section:contains("Review"), div:contains("Review")').text().trim(),
      packagesSection: $('section:contains("Packages"), div:contains("Packages"), .packages, #packages, section:contains("Plans"), div:contains("Plans"), .plans, #plans, section:contains("Pricing"), div:contains("Pricing"), .pricing, #pricing').text().trim(),
      
      // Extract all paragraph text
      paragraphs: $('p').map((_, el) => $(el).text().trim()).get().filter(text => text.length > 20),
      
      // Extract list items (often contain services/features)
      listItems: $('li').map((_, el) => $(el).text().trim()).get().filter(text => text.length > 10),
      
      // Extract headings
      headings: $('h1, h2, h3, h4, h5, h6').map((_, el) => $(el).text().trim()).get().filter(text => text.length > 0),
      
      // Extract any text that might contain business info
      mainContent: $('main, .main, .content, .container').text().trim(),
      
      // Extract contact information
      emails: html.match(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g) || [],
      phones: html.match(/(\+?1[-.\s]?)?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}/g) || [],
      
      // Extract social media links
      socialLinks: {
        facebook: $('a[href*="facebook.com"]').attr('href') || '',
        instagram: $('a[href*="instagram.com"]').attr('href') || '',
        twitter: $('a[href*="twitter.com"], a[href*="x.com"]').attr('href') || '',
        linkedin: $('a[href*="linkedin.com"]').attr('href') || '',
      }
    };

    // Create a comprehensive text summary
    const allText = [
      extractedContent.title,
      extractedContent.metaDescription,
      extractedContent.heroSection,
      extractedContent.aboutSection,
      extractedContent.servicesSection,
      extractedContent.contactSection,
      extractedContent.teamSection,
      extractedContent.testimonialsSection,
      extractedContent.packagesSection,
      ...extractedContent.paragraphs.slice(0, 20), // Limit paragraphs
      ...extractedContent.listItems.slice(0, 30), // Limit list items
      ...extractedContent.headings.slice(0, 20), // Limit headings
      extractedContent.mainContent.substring(0, 2000) // Limit main content
    ].filter(text => text && text.length > 0).join('\n\n');

    // Clean up the text
    const cleanedText = allText
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .replace(/\n\s*\n/g, '\n') // Replace multiple newlines with single newline
      .trim()
      .substring(0, 10000); // Limit total length

    console.log(`✅ Successfully scraped ${validUrl} - ${cleanedText.length} characters extracted`);

    return NextResponse.json({
      success: true,
      content: cleanedText,
      extractedContent,
      url: validUrl
    });

  } catch (error) {
    console.error('❌ Website scraping error:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    // Handle specific error types
    if (errorMessage.includes('AbortError') || errorMessage.includes('timeout')) {
      return NextResponse.json({
        error: 'Website took too long to respond. Please try again.',
        timeout: true
      }, { status: 200 }); // Return 200 so we can handle gracefully
    }
    
    if (errorMessage.includes('ENOTFOUND') || errorMessage.includes('ECONNREFUSED')) {
      return NextResponse.json({
        error: 'Website could not be reached. Please check the URL.',
        unreachable: true
      }, { status: 200 }); // Return 200 so we can handle gracefully
    }
    
    return NextResponse.json({
      error: `Failed to scrape website: ${errorMessage}`,
      blocked: errorMessage.includes('403') || errorMessage.includes('blocked')
    }, { status: 200 }); // Return 200 so we can handle gracefully
  }
}
