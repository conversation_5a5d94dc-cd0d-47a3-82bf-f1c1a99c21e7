// Script to create a test user directly in MongoDB
const { MongoClient } = require('mongodb');
const bcrypt = require('bcryptjs');
require('dotenv').config({ path: '.env.local' });

async function createTestUser() {
  const mongoUri = process.env.DATABASE || process.env.MONGODB_URI;
  
  if (!mongoUri) {
    console.error('❌ No MongoDB URI found in environment variables');
    process.exit(1);
  }

  console.log('🔌 Connecting to MongoDB...');
  const client = new MongoClient(mongoUri);
  
  try {
    await client.connect();
    const db = client.db('nevis_ai');
    const usersCollection = db.collection('users');
    
    // Check if user already exists
    const email = '<EMAIL>';
    const existingUser = await usersCollection.findOne({ email });
    
    if (existingUser) {
      console.log('✅ Test user already exists:', email);
      console.log('👤 User ID:', existingUser.userId);
      return;
    }
    
    // Create test user
    const password = 'password123';
    const hashedPassword = await bcrypt.hash(password, 10);
    const userId = 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    
    const userData = {
      userId,
      email,
      displayName: 'Test User',
      photoURL: '',
      hashedPassword,
      preferences: {
        theme: 'system',
        notifications: true,
        autoSave: true,
      },
      subscription: {
        plan: 'free',
        status: 'active',
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    await usersCollection.insertOne(userData);
    
    console.log('✅ Test user created successfully!');
    console.log('📧 Email:', email);
    console.log('🔑 Password:', password);
    console.log('👤 User ID:', userId);
    console.log('');
    console.log('🚀 You can now try logging in with these credentials');
    
  } catch (error) {
    console.error('❌ Error creating test user:', error);
  } finally {
    await client.close();
  }
}

createTestUser();
