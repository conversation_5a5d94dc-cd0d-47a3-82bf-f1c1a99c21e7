// Script to check database size and collections
const { MongoClient } = require('mongodb');
require('dotenv').config({ path: '.env.local' });

async function checkDatabaseSize() {
  const mongoUri = process.env.DATABASE || process.env.MONGODB_URI;
  const client = new MongoClient(mongoUri);

  try {
    await client.connect();
    const db = client.db('nevis_ai');

    console.log('📊 Database Size Analysis');
    console.log('========================');

    // Get database stats
    const stats = await db.stats();
    console.log(`📁 Database: ${stats.db}`);
    console.log(`💾 Data Size: ${(stats.dataSize / 1024 / 1024).toFixed(2)} MB`);
    console.log(`🗃️  Storage Size: ${(stats.storageSize / 1024 / 1024).toFixed(2)} MB`);
    console.log(`📄 Collections: ${stats.collections}`);
    console.log(`📋 Objects: ${stats.objects}`);
    console.log('');

    // Get collection sizes
    const collections = await db.listCollections().toArray();
    console.log('📚 Collection Sizes:');
    console.log('===================');

    for (const collection of collections) {
      const collStats = await db.collection(collection.name).stats();
      const sizeInMB = (collStats.storageSize / 1024 / 1024).toFixed(2);
      const count = collStats.count;
      console.log(`📦 ${collection.name}: ${sizeInMB} MB (${count} documents)`);
    }

    console.log('');
    console.log('🔍 Recommendations:');
    console.log('==================');

    // Find largest collections
    const collectionSizes = [];
    for (const collection of collections) {
      const collStats = await db.collection(collection.name).stats();
      collectionSizes.push({
        name: collection.name,
        size: collStats.storageSize,
        count: collStats.count
      });
    }

    collectionSizes.sort((a, b) => b.size - a.size);

    if (collectionSizes.length > 0) {
      console.log('🏆 Largest collections:');
      collectionSizes.slice(0, 3).forEach((col, index) => {
        const sizeInMB = (col.size / 1024 / 1024).toFixed(2);
        console.log(`${index + 1}. ${col.name}: ${sizeInMB} MB (${col.count} docs)`);
      });
    }

  } catch (error) {
    console.error('❌ Error checking database size:', error);
  } finally {
    await client.close();
  }
}

checkDatabaseSize();
