// Test authentication system API route
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Testing authentication system...');
    
    // Test 1: Check environment variables
    const mongoUri = process.env.DATABASE || process.env.MONGODB_URI;
    const jwtSecret = process.env.JWT_SECRET;
    
    console.log('📋 Environment check:');
    console.log('- MongoDB URI:', mongoUri ? '✅ Set' : '❌ Missing');
    console.log('- JWT Secret:', jwtSecret ? '✅ Set' : '❌ Missing');
    
    if (!mongoUri) {
      return NextResponse.json({
        success: false,
        error: 'MongoDB URI not configured',
        details: 'DATABASE or MONGODB_URI environment variable is missing'
      }, { status: 500 });
    }
    
    if (!jwtSecret) {
      return NextResponse.json({
        success: false,
        error: 'JWT Secret not configured',
        details: 'JWT_SECRET environment variable is missing'
      }, { status: 500 });
    }
    
    // Test 2: Check MongoDB connection
    try {
      console.log('🔌 Testing MongoDB connection...');
      const { connectToDatabase } = await import('@/lib/mongodb/config');
      const { db } = await connectToDatabase();
      console.log('✅ MongoDB connection successful');
      
      // Test 3: Check if users collection exists
      const collections = await db.listCollections().toArray();
      const hasUsersCollection = collections.some(col => col.name === 'users');
      console.log('👥 Users collection:', hasUsersCollection ? '✅ Exists' : '⚠️ Missing');
      
      return NextResponse.json({
        success: true,
        message: 'Authentication system test completed',
        results: {
          mongoConnection: 'Connected',
          usersCollection: hasUsersCollection ? 'Exists' : 'Missing',
          collections: collections.map(col => col.name)
        }
      });
      
    } catch (dbError) {
      console.error('❌ MongoDB connection failed:', dbError);
      return NextResponse.json({
        success: false,
        error: 'MongoDB connection failed',
        details: dbError instanceof Error ? dbError.message : 'Unknown database error'
      }, { status: 500 });
    }
    
  } catch (error) {
    console.error('❌ Auth test failed:', error);
    return NextResponse.json({
      success: false,
      error: 'Authentication test failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
